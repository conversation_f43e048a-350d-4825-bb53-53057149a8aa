# @package _global_
# Smoke test: disable logging and model checkpointing

logger:
  wandb:
    mode: disabled

callbacks:
  model_checkpoint: null
  model_checkpoint_progress: null

hydra:
  # https://hydra.cc/docs/tutorials/basic/running_your_app/logging/
  # sets level of only chosen command line loggers to 'DEBUG'
  # verbose: [src.train, src.utils.utils]

  # sets output paths for all file logs to 'logs/debug/'
  run:
    dir: ${oc.env:RESULT_DIR,${work_dir}/logs}/debug/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir: ${oc.env:RESULT_DIR,${work_dir}/logs}/debug/multirun_${now:%Y-%m-%d_%H-%M-%S}
    subdir: ${hydra.job.num}
